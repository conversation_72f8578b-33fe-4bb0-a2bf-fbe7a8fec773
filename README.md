# ADEPT-SQL-Demo

## Method Structure


![fig1](https://github.com/user-attachments/assets/a49361d2-79e8-4281-ab2a-704fd294b1f9)


## Quik Start:

-  Download M3E from https://huggingface.co/moka-ai/m3e-base/tree/main and put all model files in /m3e-base

-  create a new Python3.9 environment:
```
conda create --name [your_name] python=3.9
``` 

- install the dependences by:
```
pip install requirements.txt -r
```

- go to the working directory
```
cd ~/[your path]/ADEPT-SQL-Demo
```

- run the app:
```
streamlit run Home.py
```

## Official version
https://github.com/lilichennn/ADEPT-SQL
